package com.gientech.rcsa.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计查询DTO
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "RCSA矩阵关联要素统计查询DTO")
public class RcsaMatrixStatisticsDto {

    /**
     * 评估开始日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "评估开始日期")
    private LocalDate evaluateStartDate;

    /**
     * 评估结束日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "评估结束日期")
    private LocalDate evaluateEndDate;

    /**
     * 计划类型列表
     */
    @Schema(description = "计划类型列表")
    private List<String> planTypes;

    /**
     * 评估部门列表
     */
    @Schema(description = "评估部门列表")
    private List<String> evaluateDepartList;

    /**
     * 统计维度：department-部门维度，matrix-矩阵维度
     */
    @Schema(description = "统计维度：department-部门维度，matrix-矩阵维度")
    private String statisticDimension;
}
