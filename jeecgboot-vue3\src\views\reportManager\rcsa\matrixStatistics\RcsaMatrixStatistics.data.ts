import { BasicColumn, FormSchema } from '/@/components/Table';

// 表格列配置
export const statisticsColumns: BasicColumn[] = [
  {
    title: '评估机构名称',
    align: 'center',
    dataIndex: 'evaluateDepartName',
    width: 150,
    fixed: 'left',
    ifShow: true, // 部门维度时显示
  },
  {
    title: '矩阵名称',
    align: 'center',
    dataIndex: 'matrixName',
    width: 200,
    fixed: 'left',
    ifShow: false, // 矩阵维度时显示
  },
  {
    title: '风险总数量',
    align: 'center',
    dataIndex: 'riskTotalCount',
    width: 120,
    sorter: true,
  },
  {
    title: '控制总数量',
    align: 'center',
    dataIndex: 'controlTotalCount',
    width: 120,
    sorter: true,
  },
  {
    title: '不可接受风险数量',
    align: 'center',
    dataIndex: 'unacceptableRiskCount',
    width: 150,
    sorter: true,
    customRender: ({ record }) => {
      const count = record.unacceptableRiskCount || 0;
      const percent = record.unacceptableRiskPercent || 0;
      return `${count} (${percent}%)`;
    },
  },
  {
    title: '关联指标总数量',
    align: 'center',
    dataIndex: 'indicatorTotalCount',
    width: 140,
    sorter: true,
  },
  {
    title: '黄色预警指标数量',
    align: 'center',
    dataIndex: 'yellowAlertIndicatorCount',
    width: 150,
    sorter: true,
    customRender: ({ record }) => {
      const count = record.yellowAlertIndicatorCount || 0;
      const percent = record.yellowAlertIndicatorPercent || 0;
      return `${count} (${percent}%)`;
    },
  },
  {
    title: '红色预警指标数量',
    align: 'center',
    dataIndex: 'redAlertIndicatorCount',
    width: 150,
    sorter: true,
    customRender: ({ record }) => {
      const count = record.redAlertIndicatorCount || 0;
      const percent = record.redAlertIndicatorPercent || 0;
      return `${count} (${percent}%)`;
    },
  },
  {
    title: '监测总次数',
    align: 'center',
    dataIndex: 'monitoringTotalCount',
    width: 120,
    sorter: true,
  },
  {
    title: '黄色预警次数',
    align: 'center',
    dataIndex: 'yellowAlertCount',
    width: 140,
    sorter: true,
    customRender: ({ record }) => {
      const count = record.yellowAlertCount || 0;
      const percent = record.yellowAlertPercent || 0;
      return `${count} (${percent}%)`;
    },
  },
  {
    title: '红色预警次数',
    align: 'center',
    dataIndex: 'redAlertCount',
    width: 140,
    sorter: true,
    customRender: ({ record }) => {
      const count = record.redAlertCount || 0;
      const percent = record.redAlertPercent || 0;
      return `${count} (${percent}%)`;
    },
  },
  {
    title: '事件总数量',
    align: 'center',
    dataIndex: 'eventTotalCount',
    width: 120,
    sorter: true,
  },
  {
    title: '重大事件数量',
    align: 'center',
    dataIndex: 'majorEventCount',
    width: 140,
    sorter: true,
    customRender: ({ record }) => {
      const count = record.majorEventCount || 0;
      const percent = record.majorEventPercent || 0;
      return `${count} (${percent}%)`;
    },
  },
  {
    title: '净损失金额',
    align: 'center',
    dataIndex: 'netLossAmountFormatted',
    width: 150,
    sorter: true,
  },
];

// 查询表单配置
export const searchFormSchema: FormSchema[] = [
  {
    label: '评估期间',
    field: 'dateRange',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 8 },
  },
  {
    label: '统计维度',
    field: 'statisticDimension',
    component: 'Select',
    componentProps: {
      options: [
        { label: '部门维度', value: 'department' },
        { label: '矩阵维度', value: 'matrix' },
      ],
      placeholder: '请选择统计维度',
    },
    defaultValue: 'department',
    required: true,
    colProps: { span: 8 },
  },
  {
    label: '计划类型',
    field: 'planTypes',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'rcsa_plan_type',
      mode: 'multiple',
      placeholder: '请选择计划类型',
    },
    colProps: { span: 8 },
  },
  {
    label: '机构',
    field: 'evaluateDepartList',
    component: 'JSelectDept',
    componentProps: {
      multiple: true,
      placeholder: '请选择评估机构',
    },
    colProps: { span: 8 },
  },
];
