<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :title="tableTitle">
      <template #toolbar>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="onExportXls"> 导出</a-button>
      </template>
      <!--操作栏-->
      <template #headerTop>
        <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { statisticsColumns, searchFormSchema } from './RcsaMatrixStatistics.data';
  import { queryMatrixStatistics, getExportUrl } from './RcsaMatrixStatistics.api';

  const queryParam = reactive<any>({
    statisticDimension: 'department', // 默认部门维度
  });

  // 表格标题 - 当前日期
  const tableTitle = computed(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日 RCSA矩阵关联要素统计`;
  });

  // 列配置 - 根据统计维度动态调整
  const dynamicColumns = computed(() => {
    const cols = [...statisticsColumns];
    const statisticDimension = queryParam.statisticDimension || 'department';

    // 根据统计维度显示/隐藏列
    cols.forEach((col) => {
      if (col.dataIndex === 'matrixName') {
        col.ifShow = statisticDimension === 'matrix';
      }
      if (col.dataIndex === 'evaluateDepartName') {
        col.ifShow = statisticDimension === 'department';
      }
    });

    return cols;
  });

  //注册table数据
  const { tableContext, onExportXls } = useListPage({
    tableProps: {
      api: queryMatrixStatistics,
      columns: dynamicColumns,
      canResize: false,
      useSearchForm: false,
      showActionColumn: false,
      defSort: {
        column: 'riskTotalCount',
        order: 'desc',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        // 设置查询参数
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: () => {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hour = String(now.getHours()).padStart(2, '0');
        const minute = String(now.getMinutes()).padStart(2, '0');
        const second = String(now.getSeconds()).padStart(2, '0');
        return `RCSA矩阵关联要素统计_${year}${month}${day}_${hour}${minute}${second}`;
      },
      url: getExportUrl,
      params: queryParam,
    },
  });

  const [registerTable, { reload }] = tableContext;

  // 表单配置
  const [registerForm, { getFieldsValue, resetFields }] = useForm({
    labelWidth: 120,
    schemas: searchFormSchema,
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    fieldMapToNumber: [],
    fieldMapToTime: [['dateRange', ['evaluateStartDate', 'evaluateEndDate'], 'YYYY-MM-DD']],
    autoAdvancedLine: 3,
    actionColOptions: {
      span: 8,
    },
  });

  /**
   * 查询
   */
  function handleSubmit() {
    const values = getFieldsValue();
    Object.assign(queryParam, values);
    reload();
  }

  /**
   * 重置
   */
  function handleReset() {
    resetFields();
    Object.keys(queryParam).forEach((key) => {
      delete queryParam[key];
    });
    // 重置后保持默认统计维度
    queryParam.statisticDimension = 'department';
    reload();
  }
</script>