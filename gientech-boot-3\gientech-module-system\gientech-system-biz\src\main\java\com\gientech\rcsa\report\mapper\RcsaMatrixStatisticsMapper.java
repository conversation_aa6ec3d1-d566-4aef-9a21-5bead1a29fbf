package com.gientech.rcsa.report.mapper;

import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计Mapper
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@Mapper
public interface RcsaMatrixStatisticsMapper {

    /**
     * 查询RCSA矩阵关联要素统计数据
     *
     * @param queryDto 查询条件
     * @return 统计结果列表
     */
    List<RcsaMatrixStatisticsVo> queryMatrixStatistics(@Param("queryDto") RcsaMatrixStatisticsDto queryDto);
}
