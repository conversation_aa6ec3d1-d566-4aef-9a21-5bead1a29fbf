package com.gientech.rcsa.report;

import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.service.IRcsaMatrixStatisticsService;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.Arrays;

/**
 * @Description: RCSA矩阵关联要素统计测试类
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@SpringBootTest
@Slf4j
public class RcsaMatrixStatisticsTest {

    @Autowired
    private IRcsaMatrixStatisticsService rcsaMatrixStatisticsService;

    @Test
    public void testQueryMatrixStatistics() {
        try {
            // 创建查询条件
            RcsaMatrixStatisticsDto queryDto = new RcsaMatrixStatisticsDto();
            queryDto.setEvaluateStartDate(LocalDate.of(2024, 1, 1));
            queryDto.setEvaluateEndDate(LocalDate.of(2024, 12, 31));
            queryDto.setPlanTypes(Arrays.asList("1", "2")); // 假设的计划类型
            
            // 执行查询
            RcsaMatrixStatisticsVo result = rcsaMatrixStatisticsService.queryMatrixStatistics(queryDto);
            
            // 验证结果
            log.info("查询结果: {}", result);
            
            // 基本断言
            assert result != null;
            assert result.getRiskTotalCount() != null;
            assert result.getControlTotalCount() != null;
            assert result.getIndicatorTotalCount() != null;
            assert result.getEventTotalCount() != null;
            
            log.info("测试通过：RCSA矩阵关联要素统计查询功能正常");
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }

    @Test
    public void testQueryMatrixStatisticsWithEmptyCondition() {
        try {
            // 创建空查询条件
            RcsaMatrixStatisticsDto queryDto = new RcsaMatrixStatisticsDto();
            
            // 执行查询
            RcsaMatrixStatisticsVo result = rcsaMatrixStatisticsService.queryMatrixStatistics(queryDto);
            
            // 验证结果
            log.info("空条件查询结果: {}", result);
            
            // 基本断言
            assert result != null;
            
            log.info("测试通过：空条件查询功能正常");
            
        } catch (Exception e) {
            log.error("空条件测试失败", e);
            throw e;
        }
    }
}
