package com.gientech.rcsa.report.service;

import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;

import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计Service接口
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
public interface IRcsaMatrixStatisticsService {

    /**
     * 查询RCSA矩阵关联要素统计数据
     *
     * @param queryDto 查询条件
     * @return 统计结果列表
     */
    List<RcsaMatrixStatisticsVo> queryMatrixStatistics(RcsaMatrixStatisticsDto queryDto);
}
