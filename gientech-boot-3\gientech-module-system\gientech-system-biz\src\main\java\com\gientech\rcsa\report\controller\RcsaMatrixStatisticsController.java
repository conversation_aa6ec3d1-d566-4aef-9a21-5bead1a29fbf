package com.gientech.rcsa.report.controller;

import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.service.IRcsaMatrixStatisticsService;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计Controller
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@Tag(name = "RCSA矩阵关联要素统计")
@RestController
@RequestMapping("/rcsa/report/matrix")
@Slf4j
public class RcsaMatrixStatisticsController {

    @Autowired
    private IRcsaMatrixStatisticsService rcsaMatrixStatisticsService;

    /**
     * 查询RCSA矩阵关联要素统计数据
     *
     * @param queryDto 查询条件
     * @param req 请求参数
     * @return 统计结果列表
     */
    @AutoLog(value = "RCSA矩阵关联要素统计-查询统计数据")
    @Operation(summary = "查询RCSA矩阵关联要素统计数据")
    @GetMapping(value = "/statistics")
    public Result<List<RcsaMatrixStatisticsVo>> queryMatrixStatistics(
            RcsaMatrixStatisticsDto queryDto,
            HttpServletRequest req) {
        try {
            log.info("查询RCSA矩阵关联要素统计数据，查询条件：{}", queryDto);

            List<RcsaMatrixStatisticsVo> result = rcsaMatrixStatisticsService.queryMatrixStatistics(queryDto);

            log.info("RCSA矩阵关联要素统计查询成功，返回{}条记录", result.size());
            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询RCSA矩阵关联要素统计数据失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
