<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gientech.rcsa.report.mapper.RcsaMatrixStatisticsMapper">

    <!-- 查询RCSA矩阵关联要素统计数据 -->
    <select id="queryMatrixStatistics" resultType="com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo">
        SELECT
        <choose>
            <when test="queryDto.statisticDimension == 'matrix'">
                basic.matrix_name as matrixName,
                NULL as evaluateDepartName,
            </when>
            <otherwise>
                NULL as matrixName,
                depart.depart_name as evaluateDepartName,
            </otherwise>
        </choose>

        -- 基础信息统计
        COUNT(DISTINCT basic.id) as riskTotalCount,
        COUNT(DISTINCT control.id) as controlTotalCount,
        COUNT(DISTINCT CASE WHEN basic.remain_risk_is_accept = '0' THEN basic.id END) as unacceptableRiskCount,

        -- 指标预警统计
        COUNT(DISTINCT kri_rel.kri_id) as indicatorTotalCount,
        COUNT(DISTINCT CASE WHEN alert.alert_status = '2' THEN kri_rel.kri_id END) as yellowAlertIndicatorCount,
        COUNT(DISTINCT CASE WHEN alert.alert_status = '3' THEN kri_rel.kri_id END) as redAlertIndicatorCount,
        COUNT(alert.id) as monitoringTotalCount,
        COUNT(CASE WHEN alert.alert_status = '2' THEN 1 END) as yellowAlertCount,
        COUNT(CASE WHEN alert.alert_status = '3' THEN 1 END) as redAlertCount,

        -- 损失事件统计
        COUNT(DISTINCT event.id) as eventTotalCount,
        COUNT(DISTINCT CASE WHEN event.severity_classification IN ('1', '2') THEN event.id END) as majorEventCount,
        COALESCE(SUM(event.net_loss_money), 0) as netLossAmount

        FROM rcsa_task_basic_info basic
        LEFT JOIN rcsa_task_control_measure control ON basic.id = control.basic_id
        LEFT JOIN rcsa_task_manage task ON basic.task_id = task.id
        LEFT JOIN rcsa_plan_manage plan ON task.plan_id = plan.id
        LEFT JOIN sys_depart depart ON basic.evaluate_depart = depart.id
        LEFT JOIN kri_process_rel kri_rel ON basic.matrix_name = kri_rel.matrix_name
        LEFT JOIN kri_alert_survey alert ON kri_rel.kri_id = alert.id
        AND alert.monitoring_range >= (CURRENT_DATE - INTERVAL '1 YEAR')
        LEFT JOIN ldc_process_rel ldc_rel ON basic.matrix_name = ldc_rel.matrix_name
        LEFT JOIN ldc_event_manage event ON ldc_rel.ldc_id = event.id

        WHERE task.task_state = '3' -- 审核通过状态
        <if test="queryDto.evaluateStartDate != null">
            AND basic.evaluate_date >= #{queryDto.evaluateStartDate}
        </if>
        <if test="queryDto.evaluateEndDate != null">
            AND basic.evaluate_date &lt;= #{queryDto.evaluateEndDate}
        </if>
        <if test="queryDto.planTypes != null and queryDto.planTypes.size() > 0">
            AND plan.plan_type IN
            <foreach collection="queryDto.planTypes" item="planType" open="(" separator="," close=")">
                #{planType}
            </foreach>
        </if>
        <if test="queryDto.evaluateDepartList != null and queryDto.evaluateDepartList.size() > 0">
            AND basic.evaluate_depart IN
            <foreach collection="queryDto.evaluateDepartList" item="depart" open="(" separator="," close=")">
                #{depart}
            </foreach>
        </if>

        <choose>
            <when test="queryDto.statisticDimension == 'matrix'">
                GROUP BY basic.matrix_name
            </when>
            <otherwise>
                GROUP BY basic.evaluate_depart, depart.depart_name
            </otherwise>
        </choose>

        ORDER BY riskTotalCount DESC
    </select>

</mapper>
